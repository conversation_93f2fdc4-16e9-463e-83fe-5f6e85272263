<?php

namespace Theme25\Frontend\Controller\Common;

class Header extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'common/header');
    }

    public function index() {

        // Подготовка на данните с верижно извикване на методи
        $this->prepareBasicData()
             ->prepareNavigationData()
             ->prepareUserData()
             ->prepareMenuData();

        // Рендиране на шаблона с данните от $this->data
        $content = $this->loadView('common/header', $this->data);

        if(defined('STANDART_VIEW')) {
            return $content . '<div class="standart-view m-auto">';
        }
        
        return $content;
    }

    /**
     * Подготовка на основните данни за header-а
     */
    private function prepareBasicData() {
        // Logo и основни URL адреси
        $this->data['home_url'] = $this->getUrl('common/home');
        $this->data['logo_url'] = 'https://www.rakla.bg/image/rakla-logo.svg';
        $this->data['site_name'] = $this->getConfig('config_name', 'Ракла');

        return $this;
    }

    /**
     * Подготовка на данните за навигация
     */
    private function prepareNavigationData() {
        // Search functionality
        $this->data['search_placeholder'] = 'Търсене...';
        $this->data['ask_betty_text'] = 'Питай Бети';

        return $this;
    }

    /**
     * Подготовка на данните за потребителя
     */
    private function prepareUserData() {
        // Проверка дали потребителят е логнат
        $isCustomerLogged = $this->isCustomerLogged();

        // Подготовка на URL адресите според статуса на потребителя
        $this->prepareUserUrls($isCustomerLogged);

        // Подготовка на данните за количката
        $this->prepareCartData();

        return $this;
    }

    /**
     * Проверява дали потребителят е логнат във Frontend частта
     *
     * @return bool True ако потребителят е логнат
     */
    private function isCustomerLogged() {
        // Проверяваме дали customer обектът съществува и дали потребителят е логнат
        if (isset($this->customer) && is_object($this->customer) && method_exists($this->customer, 'isLogged')) {
            return $this->customer->isLogged();
        }

        return false;
    }

    /**
     * Подготвя URL адресите според статуса на потребителя
     *
     * @param bool $isLogged Дали потребителят е логнат
     */
    private function prepareUserUrls($isLogged) {
        if ($isLogged) {
            // Потребителят е логнат - показваме линкове към профил и wishlist
            $this->data['account_url'] = $this->getLink('account/account');
            $this->data['wishlist_url'] = $this->getLink('account/wishlist');
        } else {
            // Потребителят НЕ е логнат - пренасочваме към страницата за вход
            $this->data['account_url'] = $this->getLink('account/login');
            $this->data['wishlist_url'] = $this->getLink('account/login');
        }

        // URL към количката е винаги достъпен
        $this->data['cart_url'] = $this->getLink('checkout/cart');

        // Предаваме информацията за статуса на потребителя към шаблона
        $this->data['customer_logged'] = $isLogged;
    }

    /**
     * Подготвя данните за количката
     */
    private function prepareCartData() {
        $cartCount = 0;

        // Проверяваме дали cart обектът съществува и получаваме броя продукти
        if (isset($this->cart) && is_object($this->cart) && method_exists($this->cart, 'countProducts')) {
            $cartCount = $this->cart->countProducts();
        }

        // Предаваме броя продукти в количката към шаблона
        $this->data['cart_count'] = $cartCount;
    }

    private function prepareMenuData() {
        $this->data['menu_data'] = $this->loadController('common/mega_menu/getMegaMenuItems');
        return $this;
    }

}